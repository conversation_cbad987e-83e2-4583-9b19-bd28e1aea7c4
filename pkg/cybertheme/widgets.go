// Package cybertheme provides enhanced widgets with Material Design 3 and cyberpunk styling
// These widgets maintain the beloved cyberpunk aesthetic while improving readability and UX
package cybertheme

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// CyberButton creates an enhanced button with cyberpunk styling and Material Design 3 principles
type CyberButton struct {
	widget.Button
	theme      *CyberTheme
	buttonType string
	glowEffect bool
	textColor  color.Color
}

// NewCyberButton creates a new enhanced cyberpunk button
func NewCyberButton(text string, tapped func(), theme *CyberTheme, buttonType string) *CyberButton {
	button := &CyberButton{
		Button:     *widget.NewButton(text, tapped),
		theme:      theme,
		buttonType: buttonType,
		glowEffect: true,
		textColor:  color.NRGBA{R: 255, G: 255, B: 255, A: 255}, // High contrast white text
	}

	button.ExtendBaseWidget(button)
	return button
}

// CreateRender<PERSON> creates a custom renderer for the cyberpunk button
func (cb *CyberButton) CreateRenderer() fyne.WidgetRenderer {
	return &cyberButtonRenderer{
		button: cb,
		bg:     canvas.NewRectangle(cb.theme.primaryColor),
		text:   canvas.NewText(cb.Text, cb.theme.textOnPrimary),
	}
}

// cyberButtonRenderer implements the custom button renderer
type cyberButtonRenderer struct {
	button *CyberButton
	bg     *canvas.Rectangle
	text   *canvas.Text
}

func (r *cyberButtonRenderer) Layout(size fyne.Size) {
	r.bg.Resize(size)
	r.text.Resize(size)
	r.text.Move(fyne.NewPos(0, 0))
	r.text.Alignment = fyne.TextAlignCenter
}

func (r *cyberButtonRenderer) MinSize() fyne.Size {
	return fyne.NewSize(100, 40) // Minimum touch target size
}

func (r *cyberButtonRenderer) Refresh() {
	style := r.button.theme.CreateButtonStyle(r.button.buttonType)

	if bg, ok := style["background"].(color.Color); ok {
		r.bg.FillColor = bg
	}

	// Use enhanced text color for better visibility
	if r.button.textColor != nil {
		r.text.Color = r.button.textColor
	} else if textColor, ok := style["text"].(color.Color); ok {
		r.text.Color = textColor
	}

	r.text.Text = r.button.Text
	r.text.TextStyle.Bold = true // Make text bold for better readability
	r.bg.Refresh()
	r.text.Refresh()
}

func (r *cyberButtonRenderer) Objects() []fyne.CanvasObject {
	return []fyne.CanvasObject{r.bg, r.text}
}

func (r *cyberButtonRenderer) Destroy() {}

// CyberCard creates a Material Design 3 card with cyberpunk styling
type CyberCard struct {
	widget.BaseWidget
	theme     *CyberTheme
	content   fyne.CanvasObject
	elevation int
	title     string
}

// NewCyberCard creates a new cyberpunk-styled card
func NewCyberCard(title string, content fyne.CanvasObject, theme *CyberTheme, elevation int) *CyberCard {
	card := &CyberCard{
		theme:     theme,
		content:   content,
		elevation: elevation,
		title:     title,
	}

	card.ExtendBaseWidget(card)
	return card
}

// CreateRenderer creates a custom renderer for the cyberpunk card
func (cc *CyberCard) CreateRenderer() fyne.WidgetRenderer {
	elevationColors := cc.theme.GetElevationColors()
	bgColor := elevationColors[cc.elevation]

	bg := canvas.NewRectangle(bgColor)
	bg.CornerRadius = 12 // Material Design 3 card radius

	titleText := canvas.NewText(cc.title, cc.theme.textColor)
	titleText.TextStyle.Bold = true
	titleText.TextSize = cc.theme.GetTypographyScale()["title_medium"]

	return &cyberCardRenderer{
		card:      cc,
		bg:        bg,
		titleText: titleText,
		content:   cc.content,
	}
}

// cyberCardRenderer implements the custom card renderer
type cyberCardRenderer struct {
	card      *CyberCard
	bg        *canvas.Rectangle
	titleText *canvas.Text
	content   fyne.CanvasObject
}

func (r *cyberCardRenderer) Layout(size fyne.Size) {
	r.bg.Resize(size)

	padding := r.card.theme.Size(fyne.ThemeSizeName("padding"))
	titleHeight := r.titleText.TextSize + padding

	r.titleText.Move(fyne.NewPos(padding, padding))
	r.titleText.Resize(fyne.NewSize(size.Width-2*padding, titleHeight))

	if r.content != nil {
		contentY := titleHeight + padding
		r.content.Move(fyne.NewPos(padding, contentY))
		r.content.Resize(fyne.NewSize(size.Width-2*padding, size.Height-contentY-padding))
	}
}

func (r *cyberCardRenderer) MinSize() fyne.Size {
	padding := r.card.theme.Size(fyne.ThemeSizeName("padding"))
	titleHeight := r.titleText.TextSize + padding

	contentSize := fyne.NewSize(0, 0)
	if r.content != nil {
		contentSize = r.content.MinSize()
	}

	return fyne.NewSize(
		fyne.Max(200, contentSize.Width+2*padding),
		titleHeight+contentSize.Height+2*padding,
	)
}

func (r *cyberCardRenderer) Refresh() {
	elevationColors := r.card.theme.GetElevationColors()
	r.bg.FillColor = elevationColors[r.card.elevation]
	r.titleText.Color = r.card.theme.textColor
	r.titleText.Text = r.card.title

	r.bg.Refresh()
	r.titleText.Refresh()
	if r.content != nil {
		r.content.Refresh()
	}
}

func (r *cyberCardRenderer) Objects() []fyne.CanvasObject {
	objects := []fyne.CanvasObject{r.bg, r.titleText}
	if r.content != nil {
		objects = append(objects, r.content)
	}
	return objects
}

func (r *cyberCardRenderer) Destroy() {}

// CyberStatusIndicator creates an enhanced status indicator with glow effects
type CyberStatusIndicator struct {
	widget.BaseWidget
	theme  *CyberTheme
	status string
	label  string
}

// NewCyberStatusIndicator creates a new cyberpunk status indicator
func NewCyberStatusIndicator(label, status string, theme *CyberTheme) *CyberStatusIndicator {
	indicator := &CyberStatusIndicator{
		theme:  theme,
		status: status,
		label:  label,
	}

	indicator.ExtendBaseWidget(indicator)
	return indicator
}

// SetStatus updates the status indicator
func (csi *CyberStatusIndicator) SetStatus(status string) {
	csi.status = status
	csi.Refresh()
}

// CreateRenderer creates a custom renderer for the status indicator
func (csi *CyberStatusIndicator) CreateRenderer() fyne.WidgetRenderer {
	statusColors := csi.theme.GetStatusColors()
	statusColor := statusColors[csi.status]

	indicator := canvas.NewCircle(statusColor)
	indicator.Resize(fyne.NewSize(12, 12))

	labelText := canvas.NewText(csi.label, csi.theme.textColor)
	statusText := canvas.NewText(csi.status, statusColor)
	statusText.TextStyle.Bold = true

	return &cyberStatusRenderer{
		indicator:   csi,
		circle:      indicator,
		labelText:   labelText,
		statusText:  statusText,
	}
}

// cyberStatusRenderer implements the custom status indicator renderer
type cyberStatusRenderer struct {
	indicator  *CyberStatusIndicator
	circle     *canvas.Circle
	labelText  *canvas.Text
	statusText *canvas.Text
}

func (r *cyberStatusRenderer) Layout(size fyne.Size) {
	padding := r.indicator.theme.Size(fyne.ThemeSizeName("padding"))
	circleSize := float32(12)

	r.circle.Move(fyne.NewPos(0, (size.Height-circleSize)/2))
	r.circle.Resize(fyne.NewSize(circleSize, circleSize))

	textX := circleSize + padding/2
	r.labelText.Move(fyne.NewPos(textX, 0))
	r.labelText.Resize(fyne.NewSize(size.Width-textX, size.Height/2))

	r.statusText.Move(fyne.NewPos(textX, size.Height/2))
	r.statusText.Resize(fyne.NewSize(size.Width-textX, size.Height/2))
}

func (r *cyberStatusRenderer) MinSize() fyne.Size {
	return fyne.NewSize(120, 40)
}

func (r *cyberStatusRenderer) Refresh() {
	statusColors := r.indicator.theme.GetStatusColors()
	statusColor := statusColors[r.indicator.status]

	r.circle.FillColor = statusColor
	r.statusText.Color = statusColor
	r.statusText.Text = r.indicator.status
	r.labelText.Text = r.indicator.label

	r.circle.Refresh()
	r.statusText.Refresh()
	r.labelText.Refresh()
}

func (r *cyberStatusRenderer) Objects() []fyne.CanvasObject {
	return []fyne.CanvasObject{r.circle, r.labelText, r.statusText}
}

func (r *cyberStatusRenderer) Destroy() {}

// CreateCyberContainer creates a container with enhanced cyberpunk styling
func CreateCyberContainer(theme *CyberTheme, objects ...fyne.CanvasObject) *fyne.Container {
	bg := canvas.NewRectangle(theme.surfaceColor)
	bg.CornerRadius = 8

	content := container.NewWithoutLayout(bg)
	for _, obj := range objects {
		content.Add(obj)
	}

	return content
}

// CreateCyberSeparator creates an enhanced separator with cyberpunk styling
func CreateCyberSeparator(theme *CyberTheme) *canvas.Rectangle {
	separator := canvas.NewRectangle(theme.GetNeonColor("blue"))
	separator.Resize(fyne.NewSize(0, theme.Size(fyne.ThemeSizeName("separator_thickness"))))
	return separator
}
