// Package cybertheme provides the cyberpunk aesthetic theme for Assistant-Go
// Enhanced with Material Design 3 principles while maintaining the beloved cyberpunk aesthetic
package cybertheme

import (
	"fmt"
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
)

// CyberTheme implements the enhanced cyberpunk aesthetic theme
// Integrates Material Design 3 principles with cyberpunk styling for better readability and visual appeal
type CyberTheme struct {
	// Core Material Design 3 colors adapted for cyberpunk
	primaryColor     color.Color
	primaryContainer color.Color
	secondaryColor   color.Color
	backgroundColor  color.Color
	surfaceColor     color.Color
	surfaceVariant   color.Color

	// Enhanced text colors with better contrast ratios
	textColor       color.Color
	textSecondary   color.Color
	textDisabled    color.Color
	textOnPrimary   color.Color
	textOnSurface   color.Color

	// Status colors following Material Design 3 with cyberpunk enhancement
	successColor color.Color
	warningColor color.Color
	errorColor   color.Color
	infoColor    color.Color

	// Special cyberpunk neon colors for accents and effects
	neonGreen     color.Color
	neonBlue      color.Color
	neonPink      color.Color
	neonCyan      color.Color
	terminalGreen color.Color

	// Interactive state colors for better UX
	hoverColor    color.Color
	pressedColor  color.Color
	focusColor    color.Color
	selectedColor color.Color

	// Font settings with typography scale
	fontFamily string
	fontSize   float32
}

// NewCyberTheme creates an enhanced cyberpunk theme with Material Design 3 integration
// Maintains the beloved blue-dominant cyberpunk aesthetic with improved readability
func NewCyberTheme() *CyberTheme {
	return &CyberTheme{
		// Core Material Design 3 colors adapted for cyberpunk
		primaryColor:     color.NRGBA{R: 0, G: 150, B: 255, A: 255},   // Material Blue 400 (cyberpunk blue)
		primaryContainer: color.NRGBA{R: 0, G: 50, B: 100, A: 255},    // Dark blue container
		secondaryColor:   color.NRGBA{R: 0, G: 255, B: 255, A: 255},   // Bright cyan accent
		backgroundColor:  color.NRGBA{R: 0, G: 0, B: 0, A: 255},       // Pure black background
		surfaceColor:     color.NRGBA{R: 8, G: 12, B: 20, A: 255},     // Very dark blue surface
		surfaceVariant:   color.NRGBA{R: 15, G: 20, B: 30, A: 255},    // Slightly lighter surface

		// Enhanced text colors with improved contrast ratios for better visibility (WCAG AA compliant)
		textColor:     color.NRGBA{R: 200, G: 255, B: 220, A: 255},    // Brighter green for better readability (contrast ratio > 7:1)
		textSecondary: color.NRGBA{R: 180, G: 240, B: 255, A: 255},    // Brighter blue secondary (contrast ratio > 7:1)
		textDisabled:  color.NRGBA{R: 120, G: 140, B: 160, A: 255},    // Lighter muted blue-gray (contrast ratio > 4.5:1)
		textOnPrimary: color.NRGBA{R: 255, G: 255, B: 255, A: 255},    // Pure white text on primary for maximum contrast
		textOnSurface: color.NRGBA{R: 240, G: 255, B: 250, A: 255},    // Very bright light green on surface

		// Status colors following Material Design 3 with cyberpunk enhancement
		successColor: color.NRGBA{R: 0, G: 255, B: 100, A: 255},       // Bright green success
		warningColor: color.NRGBA{R: 255, G: 193, B: 7, A: 255},       // Material Amber 500
		errorColor:   color.NRGBA{R: 255, G: 82, B: 82, A: 255},       // Material Red 400
		infoColor:    color.NRGBA{R: 100, G: 200, B: 255, A: 255},     // Light blue info

		// Special cyberpunk neon colors for accents and effects
		neonGreen:     color.NRGBA{R: 0, G: 255, B: 100, A: 255},      // Softer green neon
		neonBlue:      color.NRGBA{R: 0, G: 150, B: 255, A: 255},      // Primary blue neon
		neonPink:      color.NRGBA{R: 255, G: 20, B: 147, A: 255},     // Deep pink neon
		neonCyan:      color.NRGBA{R: 0, G: 255, B: 255, A: 255},      // Bright cyan neon
		terminalGreen: color.NRGBA{R: 100, G: 255, B: 150, A: 255},    // Softer terminal green

		// Interactive state colors for better UX feedback
		hoverColor:    color.NRGBA{R: 0, G: 150, B: 255, A: 40},       // Semi-transparent blue hover
		pressedColor:  color.NRGBA{R: 0, G: 200, B: 255, A: 255},      // Bright blue when pressed
		focusColor:    color.NRGBA{R: 0, G: 255, B: 100, A: 180},      // Green focus ring
		selectedColor: color.NRGBA{R: 0, G: 255, B: 100, A: 60},       // Semi-transparent green selection

		// Font settings - monospace for cyberpunk feel with better sizing
		fontFamily: "JetBrains Mono",
		fontSize:   13.0, // Slightly larger for better readability
	}
}

// NewCustomCyberTheme creates an enhanced cyberpunk theme with custom colors
// Maintains Material Design 3 principles while allowing customization
func NewCustomCyberTheme(primary, secondary, background string) *CyberTheme {
	theme := NewCyberTheme()

	if primary != "" {
		if c, err := parseHexColor(primary); err == nil {
			theme.primaryColor = c
			theme.neonBlue = c
			// Derive a softer version for text
			if nrgba, ok := c.(color.NRGBA); ok {
				// Create a softer green variant for better readability
				theme.textColor = color.NRGBA{
					R: uint8(float32(nrgba.R) * 0.4),
					G: 255,
					B: uint8(150 + float32(nrgba.B)*0.3),
					A: 255,
				}
			}
		}
	}

	if secondary != "" {
		if c, err := parseHexColor(secondary); err == nil {
			theme.secondaryColor = c
			theme.neonCyan = c
		}
	}

	if background != "" {
		if c, err := parseHexColor(background); err == nil {
			theme.backgroundColor = c
			// Derive surface colors from background
			if nrgba, ok := c.(color.NRGBA); ok {
				theme.surfaceColor = color.NRGBA{
					R: uint8(int(nrgba.R) + 8),
					G: uint8(int(nrgba.G) + 12),
					B: uint8(int(nrgba.B) + 20),
					A: 255,
				}
				theme.surfaceVariant = color.NRGBA{
					R: uint8(int(nrgba.R) + 15),
					G: uint8(int(nrgba.G) + 20),
					B: uint8(int(nrgba.B) + 30),
					A: 255,
				}
			}
		}
	}

	return theme
}

// Color returns enhanced colors for various UI elements with Material Design 3 integration
func (t *CyberTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	// Primary colors with Material Design 3 support
	case theme.ColorNamePrimary:
		return t.primaryColor
	case theme.ColorNameBackground:
		return t.backgroundColor

	// Enhanced text colors with better contrast
	case theme.ColorNameForeground:
		return t.textColor

	// Status colors following Material Design 3
	case theme.ColorNameSuccess:
		return t.successColor
	case theme.ColorNameWarning:
		return t.warningColor
	case theme.ColorNameError:
		return t.errorColor

	// Input and interactive elements with enhanced styling
	case theme.ColorNameInputBackground:
		return t.surfaceColor
	case theme.ColorNameInputBorder:
		return t.primaryColor
	case theme.ColorNameButton:
		return color.NRGBA{R: 0, G: 120, B: 200, A: 255} // Darker blue for better text contrast
	case theme.ColorNameDisabled:
		return t.textDisabled
	case theme.ColorNamePlaceHolder:
		return t.textSecondary

	// Enhanced interactive states for better UX
	case theme.ColorNameSelection:
		return t.selectedColor
	case theme.ColorNameFocus:
		return t.focusColor
	case theme.ColorNamePressed:
		return t.pressedColor
	case theme.ColorNameHover:
		return t.hoverColor

	// Scrollbar and separators with improved visibility
	case theme.ColorNameScrollBar:
		return t.textSecondary
	case theme.ColorNameSeparator:
		return color.NRGBA{R: 0, G: 150, B: 255, A: 80} // Subtle blue separator

	// Menu and overlay with better contrast
	case theme.ColorNameMenuBackground:
		return t.surfaceVariant
	case theme.ColorNameOverlayBackground:
		return color.NRGBA{R: 0, G: 0, B: 0, A: 200} // Darker overlay

	// Header and container backgrounds (using available color names)
	case theme.ColorNameHeaderBackground:
		return t.surfaceVariant

	default:
		// Fallback to primary color for unknown colors
		return t.primaryColor
	}
}

// Font returns the font for various text elements
func (t *CyberTheme) Font(style fyne.TextStyle) fyne.Resource {
	// TODO: Load custom cyberpunk fonts
	// For now, return default font - will be enhanced later
	return theme.DefaultTheme().Font(style)
}

// Icon returns icons with cyberpunk styling
func (t *CyberTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	// TODO: Create custom cyberpunk icons
	// For now, return default icons - will be enhanced later
	return theme.DefaultTheme().Icon(name)
}

// Size returns enhanced sizes following Material Design 3 typography and spacing guidelines
func (t *CyberTheme) Size(name fyne.ThemeSizeName) float32 {
	switch name {
	// Typography scale following Material Design 3
	case theme.SizeNameText:
		return t.fontSize // Body Medium (13px)
	case theme.SizeNameCaptionText:
		return t.fontSize - 1 // Caption (12px)
	case theme.SizeNameHeadingText:
		return t.fontSize + 9 // Headline Medium (22px)
	case theme.SizeNameSubHeadingText:
		return t.fontSize + 3 // Title Medium (16px)

	// Icon sizes with better proportions
	case theme.SizeNameInlineIcon:
		return t.fontSize + 2 // Slightly larger for better visibility

	// Enhanced spacing following Material Design 3 with improved layout
	case theme.SizeNamePadding:
		return 16 // Increased padding for better spacing and touch targets

	// Scrollbar improvements
	case theme.SizeNameScrollBar:
		return 14 // Slightly wider for easier interaction
	case theme.SizeNameScrollBarSmall:
		return 10

	// Border and radius improvements
	case theme.SizeNameSeparatorThickness:
		return 1.5 // Slightly thicker for better visibility
	case theme.SizeNameInputBorder:
		return 2
	case theme.SizeNameInputRadius:
		return 8 // More rounded corners following Material Design 3
	case theme.SizeNameSelectionRadius:
		return 6

	// Additional sizing for better UX and spacing
	case theme.SizeNameInnerPadding:
		return 12 // Increased inner padding for better component spacing

	default:
		return theme.DefaultTheme().Size(name)
	}
}

// GetNeonColor returns special neon colors for custom widgets and effects
func (t *CyberTheme) GetNeonColor(name string) color.Color {
	switch name {
	case "green":
		return t.neonGreen
	case "blue":
		return t.neonBlue
	case "pink":
		return t.neonPink
	case "cyan":
		return t.neonCyan
	case "terminal":
		return t.terminalGreen
	default:
		return t.primaryColor
	}
}

// GetInteractiveColor returns colors for interactive states
func (t *CyberTheme) GetInteractiveColor(state string) color.Color {
	switch state {
	case "hover":
		return t.hoverColor
	case "pressed":
		return t.pressedColor
	case "focus":
		return t.focusColor
	case "selected":
		return t.selectedColor
	default:
		return t.primaryColor
	}
}

// SetFontSize updates the font size
func (t *CyberTheme) SetFontSize(size float32) {
	t.fontSize = size
}

// SetPrimaryColor updates the primary color
func (t *CyberTheme) SetPrimaryColor(c color.Color) {
	t.primaryColor = c
	t.textColor = c
	t.neonGreen = c
}

// SetSecondaryColor updates the secondary color
func (t *CyberTheme) SetSecondaryColor(c color.Color) {
	t.secondaryColor = c
	t.neonPink = c
}

// parseHexColor parses a hex color string to color.Color
func parseHexColor(hex string) (color.Color, error) {
	// Remove # if present
	if len(hex) > 0 && hex[0] == '#' {
		hex = hex[1:]
	}

	// Parse hex values
	if len(hex) != 6 {
		return nil, fmt.Errorf("invalid hex color format")
	}

	var r, g, b uint8
	if _, err := fmt.Sscanf(hex, "%02x%02x%02x", &r, &g, &b); err != nil {
		return nil, fmt.Errorf("failed to parse hex color: %w", err)
	}

	return color.NRGBA{R: r, G: g, B: b, A: 255}, nil
}

// ApplyToApp applies the cyberpunk theme to a Fyne app
func (t *CyberTheme) ApplyToApp(app fyne.App) {
	app.Settings().SetTheme(t)
}

// CreateGlowEffect creates a glow effect color for neon elements
func (t *CyberTheme) CreateGlowEffect(baseColor color.Color, intensity float32) color.Color {
	if nrgba, ok := baseColor.(color.NRGBA); ok {
		// Create a glow effect by adjusting alpha
		alpha := uint8(float32(nrgba.A) * intensity)
		return color.NRGBA{R: nrgba.R, G: nrgba.G, B: nrgba.B, A: alpha}
	}
	return baseColor
}

// GetTerminalColors returns colors specifically for terminal-like widgets
func (t *CyberTheme) GetTerminalColors() (background, text, cursor color.Color) {
	return color.NRGBA{R: 0, G: 0, B: 0, A: 255},        // Black background
		   t.terminalGreen,                                // Green text
		   color.NRGBA{R: 0, G: 255, B: 0, A: 200}        // Blinking cursor
}

// GetStatusColors returns enhanced status colors with better visual hierarchy
func (t *CyberTheme) GetStatusColors() map[string]color.Color {
	return map[string]color.Color{
		"success":    t.successColor,
		"warning":    t.warningColor,
		"error":      t.errorColor,
		"info":       t.infoColor,
		"processing": t.neonPink,
		"pending":    t.textSecondary,
		"online":     t.neonGreen,
		"offline":    t.textDisabled,
		"connecting": t.neonCyan,
	}
}

// GetTypographyScale returns Material Design 3 typography scale
func (t *CyberTheme) GetTypographyScale() map[string]float32 {
	return map[string]float32{
		"display_large":    t.fontSize + 44, // 57px
		"display_medium":   t.fontSize + 32, // 45px
		"display_small":    t.fontSize + 23, // 36px
		"headline_large":   t.fontSize + 19, // 32px
		"headline_medium":  t.fontSize + 15, // 28px
		"headline_small":   t.fontSize + 11, // 24px
		"title_large":      t.fontSize + 9,  // 22px
		"title_medium":     t.fontSize + 3,  // 16px
		"title_small":      t.fontSize + 1,  // 14px
		"body_large":       t.fontSize + 3,  // 16px
		"body_medium":      t.fontSize,      // 13px (base)
		"body_small":       t.fontSize - 1,  // 12px
		"label_large":      t.fontSize + 1,  // 14px
		"label_medium":     t.fontSize,      // 13px
		"label_small":      t.fontSize - 2,  // 11px
	}
}

// GetElevationColors returns colors for different elevation levels
func (t *CyberTheme) GetElevationColors() map[int]color.Color {
	return map[int]color.Color{
		0: t.surfaceColor,                                          // Surface level
		1: color.NRGBA{R: 15, G: 20, B: 30, A: 255},               // Elevation 1
		2: color.NRGBA{R: 20, G: 25, B: 35, A: 255},               // Elevation 2
		3: color.NRGBA{R: 25, G: 30, B: 40, A: 255},               // Elevation 3
		4: color.NRGBA{R: 30, G: 35, B: 45, A: 255},               // Elevation 4
		5: color.NRGBA{R: 35, G: 40, B: 50, A: 255},               // Elevation 5
	}
}

// CreateButtonStyle returns enhanced styling for different button types with better text visibility
func (t *CyberTheme) CreateButtonStyle(buttonType string) map[string]interface{} {
	switch buttonType {
	case "primary":
		return map[string]interface{}{
			"background": t.primaryColor,
			"text":       color.NRGBA{R: 255, G: 255, B: 255, A: 255}, // Pure white for maximum contrast
			"border":     t.primaryColor,
			"radius":     8.0,
			"padding":    16.0,
		}
	case "secondary":
		return map[string]interface{}{
			"background": color.NRGBA{R: 0, G: 80, B: 150, A: 255},    // Darker blue background for better contrast
			"text":       color.NRGBA{R: 255, G: 255, B: 255, A: 255}, // Pure white for maximum contrast
			"border":     t.primaryColor,
			"radius":     8.0,
			"padding":    16.0,
		}
	case "outlined":
		return map[string]interface{}{
			"background": color.NRGBA{R: 0, G: 0, B: 0, A: 100}, // Semi-transparent background for better text visibility
			"text":       color.NRGBA{R: 150, G: 255, B: 180, A: 255}, // Bright green text
			"border":     t.primaryColor,
			"radius":     8.0,
			"padding":    16.0,
		}
	case "text":
		return map[string]interface{}{
			"background": color.Transparent,
			"text":       color.NRGBA{R: 150, G: 255, B: 180, A: 255}, // Bright green text
			"border":     color.Transparent,
			"radius":     8.0,
			"padding":    12.0,
		}
	default:
		return map[string]interface{}{
			"background": t.primaryColor,
			"text":       color.NRGBA{R: 255, G: 255, B: 255, A: 255}, // Pure white for maximum contrast
			"border":     t.primaryColor,
			"radius":     8.0,
			"padding":    16.0,
		}
	}
}


